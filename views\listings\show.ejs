<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PG Dedo - <%= listing.title %></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Mapbox CSS -->
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <%- include('../includes/navbar') %>

    <!-- Main Content -->
    <main>
        <div class="container">
            <%- include('../includes/flash') %>
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/listings">All PGs</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><%= listing.title %></li>
                        </ol>
                    </nav>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card listing-detail-card">
                        <img src="<%= listing.image %>" class="listing-image" alt="<%= listing.title %>">
                        <div class="card-body">
                            <h2 class="card-title mb-4"><%= listing.title %></h2>

                            <% if (listing.author) { %>
                            <div class="detail-item">
                                <h6><i class="fas fa-user detail-icon me-2"></i>Owner Information</h6>
                                <p class="mb-1"><strong>Name:</strong> <%= listing.author.fullName %></p>
                                <p class="mb-0"><strong>Email:</strong> <a href="mailto:<%= listing.author.email %>" class="text-decoration-none"><%= listing.author.email %></a></p>
                            </div>
                            <% } else if (listing.owner && listing.owner.name) { %>
                            <div class="detail-item">
                                <h6><i class="fas fa-user detail-icon me-2"></i>Owner Information</h6>
                                <p class="mb-1"><strong>Name:</strong> <%= listing.owner.name %></p>
                                <p class="mb-0"><strong>Contact:</strong> <a href="tel:<%= listing.owner.contact %>" class="text-decoration-none"><%= listing.owner.contact %></a></p>
                            </div>
                            <% } else { %>
                            <div class="detail-item">
                                <h6><i class="fas fa-user detail-icon me-2"></i>Owner Information</h6>
                                <p class="mb-1"><strong>Name:</strong> Rajesh Kumar</p>
                                <p class="mb-0"><strong>Email:</strong> <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a></p>
                            </div>
                            <% } %>

                            <div class="detail-item">
                                <h6><i class="fas fa-align-left detail-icon me-2"></i>Description</h6>
                                <p class="mb-0"><%= listing.description %></p>
                            </div>

                            <div class="detail-item">
                                <h6><i class="fas fa-map-marker-alt detail-icon me-2"></i>Location</h6>
                                <p class="mb-0"><%= listing.location %></p>
                            </div>

                            <div class="detail-item">
                                <h6><i class="fas fa-landmark detail-icon me-2"></i>Landmark</h6>
                                <p class="mb-0"><%= listing.landmark %></p>
                            </div>

                            <% if (currentUser && listing.author && currentUser._id.equals(listing.author._id)) { %>
                            <div class="mt-4">
                                <a href="/listings/<%= listing._id %>/edit" class="btn btn-primary me-2">
                                    <i class="fas fa-edit me-1"></i>Edit Listing
                                </a>

                                <form action="/listings/<%= listing._id %>?_method=DELETE" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this PG listing? This action cannot be undone.')">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash me-1"></i>Delete Listing
                                    </button>
                                </form>
                            </div>
                            <% } %>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="price-display">
                                ₹<%= listing.price.toLocaleString('en-IN') %>
                                <div style="font-size: 0.8rem; opacity: 0.9;">per month</div>
                            </div>

                            <div class="mt-4">
                                <h6>Quick Info</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-home text-primary me-2"></i>
                                        <strong>PG Name:</strong> <%= listing.title %>
                                    </li>
                                    <% if (listing.author) { %>
                                    <li class="mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <strong>Owner:</strong> <%= listing.author.fullName %>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-envelope text-primary me-2"></i>
                                        <strong>Contact:</strong> <a href="mailto:<%= listing.author.email %>" class="text-decoration-none"><%= listing.author.email %></a>
                                    </li>
                                    <% } else if (listing.owner && listing.owner.name) { %>
                                    <li class="mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <strong>Owner:</strong> <%= listing.owner.name %>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-phone text-primary me-2"></i>
                                        <strong>Contact:</strong> <a href="tel:<%= listing.owner.contact %>" class="text-decoration-none"><%= listing.owner.contact %></a>
                                    </li>
                                    <% } else { %>
                                    <li class="mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <strong>Owner:</strong> Rajesh Kumar
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-envelope text-primary me-2"></i>
                                        <strong>Contact:</strong> <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </li>
                                    <% } %>
                                    <li class="mb-2">
                                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                        <strong>Location:</strong> <%= listing.location %>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-landmark text-primary me-2"></i>
                                        <strong>Landmark:</strong> <%= listing.landmark %>
                                    </li>
                                </ul>
                            </div>

                            <% if (currentUser && listing.author && currentUser._id.equals(listing.author._id)) { %>
                                <div class="d-flex gap-2 mt-4">
                                    <a href="/listings/<%= listing._id %>/edit" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                    <form action="/listings/<%= listing._id %>?_method=DELETE" method="POST" style="display: inline;">
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this listing?')">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </form>
                                </div>
                            <% } %>

                            <div class="mt-3">
                                <a href="/listings" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-arrow-left me-1"></i>Back to All PGs
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Location Map Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card" style="background: white; border: 1px solid rgba(0, 0, 0, 0.125);">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-map-marked-alt text-primary me-2"></i>Location Map
                                <small class="text-muted ms-2">Mathura Location & GLA University</small>
                            </h4>
                        </div>
                        <div class="map-theme-toggle">
                            <button id="mapThemeToggle" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-moon me-1"></i>Dark Mode
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="map" style="height: 400px; width: 100%; background-color: #f8f9fa; border-radius: 0;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <div class="spinner-border text-primary mb-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted mb-0">Loading map...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                    <strong>Red Marker:</strong> Mathura Location
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                    <strong>Blue Marker:</strong> GLA University
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i class="fas fa-star text-warning me-2"></i>Reviews & Ratings
                                <% if (ratingData.totalReviews > 0) { %>
                                    <span class="badge bg-primary ms-2"><%= ratingData.averageRating %>/5 (<%= ratingData.totalReviews %> reviews)</span>
                                <% } %>
                            </h4>
                        </div>
                        <div class="card-body">
                            <!-- Add Review Form -->
                            <% if (currentUser) { %>
                            <div class="mb-4">
                                <h5>Add Your Review</h5>
                                <form action="/listings/<%= listing._id %>/reviews" method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="rating" class="form-label">Rating</label>
                                        <select class="form-control" id="rating" name="review[rating]" required>
                                            <option value="">Select Rating</option>
                                            <option value="1">1 Star - Poor</option>
                                            <option value="2">2 Stars - Fair</option>
                                            <option value="3">3 Stars - Good</option>
                                            <option value="4">4 Stars - Very Good</option>
                                            <option value="5">5 Stars - Excellent</option>
                                        </select>
                                        <div class="valid-feedback">Great choice!</div>
                                        <div class="invalid-feedback">Please select a rating.</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="comment" class="form-label">Your Review</label>
                                        <textarea class="form-control" id="comment" name="review[comment]" rows="4" required minlength="10" maxlength="500" placeholder="Share your experience about this PG..."></textarea>
                                        <div class="valid-feedback">Thank you for your detailed review!</div>
                                        <div class="invalid-feedback">Please provide a review between 10-500 characters.</div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>Submit Review
                                    </button>
                                </form>
                            </div>
                            <% } else { %>
                            <div class="mb-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Please <a href="/login" class="alert-link">login</a> to add a review.
                                </div>
                            </div>
                            <% } %>

                            <hr>

                            <!-- Display Reviews -->
                            <div class="reviews-section">
                                <h5>All Reviews (<%= reviews.length %>)</h5>
                                <% if (reviews.length === 0) { %>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>No reviews yet. Be the first to review this PG!
                                    </div>
                                <% } else { %>
                                    <% reviews.forEach(review => { %>
                                        <div class="review-item mb-4 p-3" style="background: rgba(42, 42, 42, 0.8); border-radius: 12px; border-left: 4px solid #007bff;">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="mb-1"><%= review.author.fullName %></h6>
                                                    <div class="rating mb-1">
                                                        <span class="text-warning"><%= review.stars %></span>
                                                        <span class="ms-2 text-muted">(<%= review.rating %>/5)</span>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted"><%= review.formattedDate %></small>
                                                    <% if (currentUser && review.author._id.equals(currentUser._id)) { %>
                                                    <form action="/listings/<%= listing._id %>/reviews/<%= review._id %>?_method=DELETE" method="POST" style="display: inline;" class="ms-2">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this review?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                    <% } %>
                                                </div>
                                            </div>
                                            <p class="mb-0"><%= review.comment %></p>
                                        </div>
                                    <% }); %>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <%- include('../includes/footer') %>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Mapbox JS -->
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>

    <!-- Mapbox Map Script -->
    <script>
        // Initialize Mapbox map
        mapboxgl.accessToken = 'pk.eyJ1IjoiaXRzc2FyYW5oZXJlIiwiYSI6ImNsd3B3aDFybjFodTMyaXJ6cGQxeWdwYzcifQ.4HPJRlRvgTdHaXXTDQEWCg';

        // GLA University coordinates
        const collegeCoords = [77.5946, 27.5916];

        // Mathura city center coordinates (red marker)
        const mathuraCoords = [77.6737, 27.4924];

        // PG coordinates from listing (defaults to Mathura center)
        const pgLat = <%= listing.coordinates && listing.coordinates.latitude ? listing.coordinates.latitude : 27.4924 %>;
        const pgLng = <%= listing.coordinates && listing.coordinates.longitude ? listing.coordinates.longitude : 77.6737 %>;
        const pgCoords = [pgLng, pgLat];

        console.log('Initializing map with coordinates:', pgCoords);

        // Map theme configuration
        let isDarkMode = false;
        const lightStyle = 'mapbox://styles/mapbox/streets-v12';
        const darkStyle = 'mapbox://styles/mapbox/dark-v11';

        try {
            const map = new mapboxgl.Map({
                container: 'map',
                style: lightStyle,
                center: mathuraCoords,
                zoom: 12
            });

            console.log('Map created successfully');

            // Wait for map to load before adding markers
            map.on('load', function() {
                console.log('Map loaded successfully');

                // Add navigation controls
                map.addControl(new mapboxgl.NavigationControl());

                // Add GLA University marker (blue) - always visible
                new mapboxgl.Marker({ color: '#007bff' })
                    .setLngLat(collegeCoords)
                    .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-university"></i> GLA University</h6><p>Main College Campus<br><small>Mathura, Uttar Pradesh</small></p>'))
                    .addTo(map);

                // Add Mathura city center marker (red) - always visible
                new mapboxgl.Marker({ color: '#dc3545' })
                    .setLngLat(mathuraCoords)
                    .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-city"></i> Mathura City Center</h6><p>Main City Area<br><small>Mathura, Uttar Pradesh</small></p>'))
                    .addTo(map);

                // Add PG marker (green) - only if coordinates are different from both college and city center
                if ((pgCoords[0] !== collegeCoords[0] || pgCoords[1] !== collegeCoords[1]) &&
                    (pgCoords[0] !== mathuraCoords[0] || pgCoords[1] !== mathuraCoords[1])) {
                    new mapboxgl.Marker({ color: '#28a745' })
                        .setLngLat(pgCoords)
                        .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-home"></i> <%= listing.title %></h6><p><i class="fas fa-map-marker-alt"></i> <%= listing.location %><br><small><i class="fas fa-rupee-sign"></i> ₹<%= listing.price %>/month</small></p>'))
                        .addTo(map);

                    // Fit map to show all three markers
                    const bounds = new mapboxgl.LngLatBounds();
                    bounds.extend(collegeCoords);
                    bounds.extend(mathuraCoords);
                    bounds.extend(pgCoords);
                    map.fitBounds(bounds, { padding: 50 });
                } else {
                    // Fit map to show college and city center
                    const bounds = new mapboxgl.LngLatBounds();
                    bounds.extend(collegeCoords);
                    bounds.extend(mathuraCoords);
                    map.fitBounds(bounds, { padding: 50 });
                }

                console.log('Markers added successfully');
            });

            // Map theme toggle functionality
            const themeToggleBtn = document.getElementById('mapThemeToggle');

            function toggleMapTheme() {
                isDarkMode = !isDarkMode;
                const newStyle = isDarkMode ? darkStyle : lightStyle;

                // Update button text and icon
                if (isDarkMode) {
                    themeToggleBtn.innerHTML = '<i class="fas fa-sun me-1"></i>Light Mode';
                    themeToggleBtn.className = 'btn btn-outline-warning btn-sm';
                } else {
                    themeToggleBtn.innerHTML = '<i class="fas fa-moon me-1"></i>Dark Mode';
                    themeToggleBtn.className = 'btn btn-outline-secondary btn-sm';
                }

                // Change map style
                map.setStyle(newStyle);

                // Re-add markers after style change
                map.once('styledata', function() {
                    // Add navigation controls
                    if (!map.hasControl(map._controls[0])) {
                        map.addControl(new mapboxgl.NavigationControl());
                    }

                    // Add GLA University marker (blue)
                    new mapboxgl.Marker({ color: '#007bff' })
                        .setLngLat(collegeCoords)
                        .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-university"></i> GLA University</h6><p>Main College Campus<br><small>Mathura, Uttar Pradesh</small></p>'))
                        .addTo(map);

                    // Add Mathura city center marker (red)
                    new mapboxgl.Marker({ color: '#dc3545' })
                        .setLngLat(mathuraCoords)
                        .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-city"></i> Mathura City Center</h6><p>Main City Area<br><small>Mathura, Uttar Pradesh</small></p>'))
                        .addTo(map);

                    // Add PG marker (green) if coordinates are different
                    if ((pgCoords[0] !== collegeCoords[0] || pgCoords[1] !== collegeCoords[1]) &&
                        (pgCoords[0] !== mathuraCoords[0] || pgCoords[1] !== mathuraCoords[1])) {
                        new mapboxgl.Marker({ color: '#28a745' })
                            .setLngLat(pgCoords)
                            .setPopup(new mapboxgl.Popup().setHTML('<h6><i class="fas fa-home"></i> <%= listing.title %></h6><p><i class="fas fa-map-marker-alt"></i> <%= listing.location %><br><small><i class="fas fa-rupee-sign"></i> ₹<%= listing.price %>/month</small></p>'))
                            .addTo(map);
                    }
                });
            }

            // Add click event to theme toggle button
            themeToggleBtn.addEventListener('click', toggleMapTheme);

            // Handle map errors
            map.on('error', function(e) {
                console.error('Map error:', e);
                document.getElementById('map').innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><p class="text-muted">Map could not be loaded. Error: ' + e.error.message + '</p></div>';
            });

        } catch (error) {
            console.error('Mapbox initialization error:', error);
            document.getElementById('map').innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><p class="text-muted">Map could not be loaded. Error: ' + error.message + '</p></div>';
        }
    </script>

    <!-- Sidebar JavaScript -->
    <script>
        // Sidebar functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const sidebarClose = document.getElementById('sidebarClose');
        const main = document.querySelector('main');
        const footer = document.querySelector('footer');
        let sidebarOpen = true; // Default state is open

        function toggleSidebar() {
            if (sidebarOpen) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            sidebar.classList.remove('hidden');
            main.classList.remove('sidebar-hidden');
            if (footer) footer.classList.remove('sidebar-hidden');
            sidebarToggle.innerHTML = '<i class="fas fa-times"></i>';
            sidebarOpen = true;
        }

        function closeSidebar() {
            sidebar.classList.add('hidden');
            main.classList.add('sidebar-hidden');
            if (footer) footer.classList.add('sidebar-hidden');
            sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
            sidebarOpen = false;
        }

        // Initialize sidebar state
        sidebarToggle.innerHTML = '<i class="fas fa-times"></i>';

        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarClose.addEventListener('click', closeSidebar);
        sidebarOverlay.addEventListener('click', closeSidebar);

        // Close sidebar on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebarOpen) {
                closeSidebar();
            }
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();


    </script>
</body>
</html>
